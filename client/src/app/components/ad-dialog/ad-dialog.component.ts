import { Component, ElementRef, ViewChild, inject, PLATFORM_ID, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Subject, takeUntil } from 'rxjs';
import { AdDisplayService, Advertisement } from '../../services/ad-display.service';
import { environment } from '@/env/environment';

@Component({
  selector: 'app-ad-dialog',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './ad-dialog.component.html',
  styleUrls: ['./ad-dialog.component.scss']
})
export class AdDialogComponent implements OnInit, OnDestroy {
  @ViewChild('adDialog') adDialog!: ElementRef<HTMLDialogElement>;
  
  private platformId = inject(PLATFORM_ID);
  private sanitizer = inject(DomSanitizer);
  private adDisplayService = inject(AdDisplayService);
  private cdr = inject(ChangeDetectorRef);
  private destroy$ = new Subject<void>();
  
  environment = environment;
  currentAd: Advertisement | null = null;
  safeAdLink: SafeResourceUrl | null = null;

  ngOnInit(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Listen for ads to display
    this.adDisplayService.showAd$
      .pipe(takeUntil(this.destroy$))
      .subscribe(ad => {
        console.log('AdDialogComponent received ad:', ad);
        if (ad) {
          this.showAd(ad);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Show the advertisement dialog
   */
  private showAd(ad: Advertisement): void {
    console.log('AdDialogComponent showAd called with:', ad);
    this.currentAd = ad;

    // Prepare safe link for display
    this.safeAdLink = this.sanitizer.bypassSecurityTrustResourceUrl(ad.link);

    // Trigger change detection to update the template
    this.cdr.detectChanges();

    console.log('AdDialogComponent currentAd set to:', this.currentAd);

    // Show the dialog
    if (this.adDialog && isPlatformBrowser(this.platformId)) {
      console.log('AdDialogComponent showing modal dialog');
      this.adDialog.nativeElement.showModal();
    }
  }

  /**
   * Close the ad dialog
   */
  closeAdDialog(): void {
    if (this.adDialog) {
      this.adDialog.nativeElement.close();
    }
    this.currentAd = null;
    this.safeAdLink = null;

    // Trigger change detection to update the template
    this.cdr.detectChanges();
  }

  /**
   * Navigate to the ad link and close dialog
   */
  navigateToAdLink(): void {
    if (isPlatformBrowser(this.platformId) && this.currentAd) {
      window.open(this.currentAd.link, '_blank');
      this.closeAdDialog();
    }
  }
}
