.ad-dialog {
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 750px;
  min-height: 414px;
  border-radius: 20px;
  padding: 35px;
  border: none;
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.25s ease-out, transform 0.25s ease-out;

  &::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    transition: background-color 0.25s ease-out;
  }

  &:focus-visible {
    outline: none;
  }

  // Opening animation
  &.ad-dialog-opening {
    opacity: 0;
    transform: scale(0.9);
    animation: adDialogFadeIn 0.3s ease-out forwards;

    &::backdrop {
      background-color: rgba(0, 0, 0, 0);
      animation: backdropFadeIn 0.3s ease-out forwards;
    }
  }

  // Closing animation
  &.ad-dialog-closing {
    animation: adDialogFadeOut 0.25s ease-out forwards;

    &::backdrop {
      animation: backdropFadeOut 0.25s ease-out forwards;
    }
  }
}

// Keyframe animations
@keyframes adDialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes adDialogFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes backdropFadeIn {
  from {
    background-color: rgba(0, 0, 0, 0);
  }
  to {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

@keyframes backdropFadeOut {
  from {
    background-color: rgba(0, 0, 0, 0.5);
  }
  to {
    background-color: rgba(0, 0, 0, 0);
  }
}

.mn_title {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 36px;
  text-align: center;
  vertical-align: middle;
  color: var(--text-color23);
  margin-bottom: 17px;
}

.mn_descr {
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  text-align: center;
  vertical-align: middle;
  color: var(--text-color23);
}

.mn_title::first-letter {
  font-family: BeaumarchaisC;
  font-weight: 400;
  font-size: 45px;
  line-height: 36px;
  text-align: justify;
  color: var(--pl_start1);
}

.close-button {
  padding: 5px;
  position: absolute;
  top: 17px;
  right: 15px;
  background: none;
  border: none;
  cursor: pointer;
}

.ad-body {
  display: flex;
}

.save-btn {
  width: 234px;
  height: 50px;
  padding: 0;
  position: relative;
  margin: 45px auto 0 !important;
  background: none;
  border: none;
  cursor: pointer;

  .btn-backdrop-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }

  .save-btn-label {
    margin: 0 auto;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    padding: 14px 25px;
    color: var(--font-color1);
    position: relative;
    z-index: 1;
  }
}

.bl_cont {
  width: 50%;
  justify-content: center;
}

.ad-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 338px;
  height: 327px;
  overflow: hidden;

  img {
    width: 85%;
    height: 85%;
    object-fit: cover;
  }
}

.ad-content {
  position: relative;
  width: 100%;
}

// Responsive styles
@media (max-width: 768px) {
  .ad-dialog {
    padding: 20px;
    width: 100%;
    min-height: 360px;
  }

  .ad-image {
    width: 270px;
    height: 280px;
  }
}

@media (max-width: 620px) {
  .ad-body {
    flex-direction: column;
    align-items: center;
  }

  .bl_cont {
    width: 100%;
    margin-bottom: 25px;
  }

  .ad-dialog {
    width: 80%;
    justify-content: center;
  }
}

@media (max-width: 520px) {
  .ad-dialog {
    width: 90%;
  }
}

@media (max-width: 380px) {
  .ad-image {
    width: 230px;
    height: 230px;
  }
}
