<dialog #adDialog class="ad-dialog">
  @if (currentAd) {
    <div class="ad-content">
      <div class="ad-body">
        @if (currentAd.image) {
          <div class="ad-image">
            <img [src]="environment.serverUrl + '/upload/' + currentAd.image.name" [alt]="currentAd.title">
          </div>
        }
        <div class="flex flex-col bl_cont">
          <h3 class="mn_title">{{ currentAd.title }}</h3>
          <p class="mn_descr">{{ currentAd.description }}</p>
          <button class="save-btn" (click)="navigateToAdLink()">
            <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
            <div class="save-btn-label">Подробнее</div>
          </button>
        </div>
      </div>
      <button class="close-button" (click)="closeAdDialog()">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M0.707118 18.6424C0.316617 18.2518 0.316616 17.6186 0.707118 17.2281L17.2256 0.707637C17.6161 0.317089 18.2492 0.317089 18.6397 0.707637C19.0302 1.09818 19.0302 1.73139 18.6397 2.12193L2.12125 18.6424C1.73075 19.0329 1.09762 19.0329 0.707118 18.6424Z"
            fill="var(--pl_start1)" />
          <path fill-rule="evenodd" clip-rule="evenodd"
            d="M0.707052 0.707221C1.09755 0.316674 1.73068 0.316673 2.12118 0.707221L18.6397 17.2277C19.0302 17.6182 19.0302 18.2514 18.6397 18.642C18.2492 19.0325 17.616 19.0325 17.2255 18.642L0.707052 2.12152C0.316551 1.73097 0.316551 1.09777 0.707052 0.707221Z"
            fill="var(--pl_start1)" />
        </svg>
      </button>
    </div>
  }
</dialog>
